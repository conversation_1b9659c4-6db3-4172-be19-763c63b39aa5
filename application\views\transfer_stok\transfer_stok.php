<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-exchange-alt text-green"></i> Data Transfer Stok</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_transfer_stok" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nomor Transfer</th>
                                    <th>Tanggal</th>
                                    <th>Gudang Asal</th>
                                    <th>Gudang Tujuan</th>
                                    <th>Status</th>
                                    <th>Total Item</th>
                                    <th>Total Qty</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<!-- Bootstrap modal -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Transfer Stok</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script type="text/javascript">
    var save_method; //for save method string
    var table;

    $(document).ready(function() {
        $('.select2').select2();
        
        //datatables
        table = $("#tbl_transfer_stok").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Transfer Stok Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('TransferStok/ajax_list') ?>",
                "type": "POST"
            },

        });

        //set input/textarea/select event when change value, remove class error and remove text help block 
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
        });

    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('#modal_form').modal({
            backdrop: 'static',
            keyboard: false
        }); // show bootstrap modal
        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo site_url('TransferStok/form_input') ?>",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);

                // Enable nomor field dan show tombol generate saat add
                setTimeout(function() {
                    $('[name="nomor_transfer"]').prop('readonly', false);
                    $('#btn-generate-nomor').show();
                    
                    // Set tanggal hari ini
                    var today = new Date().toISOString().split('T')[0];
                    $('[name="tanggal_transfer"]').val(today);
                }, 100);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string

        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo site_url('TransferStok/form_input') ?>",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);
                $('#modal_form').modal({
                    backdrop: 'static',
                    keyboard: false
                }); // show bootstrap modal when complete loaded

                //Ajax Load data from ajax
                $.ajax({
                    url: "<?php echo site_url('TransferStok/edit') ?>/" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="nomor_transfer"]').val(data.nomor_transfer);
                        $('[name="tanggal_transfer"]').val(data.tanggal_transfer);
                        $('[name="gudang_asal_id"]').val(data.gudang_asal_id).trigger('change');
                        $('[name="gudang_tujuan_id"]').val(data.gudang_tujuan_id).trigger('change');
                        $('[name="keterangan"]').val(data.keterangan);

                        // Disable nomor field dan hide tombol generate saat edit
                        setTimeout(function() {
                            $('[name="nomor_transfer"]').prop('readonly', true);
                            $('#btn-generate-nomor').hide();
                        }, 100);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        alert('Error get data from ajax');
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
    }

    function reload_table() {
        table.ajax.reload(null, false); //reload datatable ajax 
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable 
        var url;

        if (save_method == 'add') {
            url = "<?php echo site_url('TransferStok/insert') ?>";
        } else {
            url = "<?php echo site_url('TransferStok/update') ?>";
        }

        // ajax adding data to database
        $.ajax({
            url: url,
            type: "POST",
            data: $('#form').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    reload_table();
                    
                    // Show success message
                    Swal.fire({
                        title: 'Berhasil!',
                        text: save_method == 'add' ? 'Data transfer stok berhasil ditambahkan' : 'Data transfer stok berhasil diupdate',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });

                    // Redirect ke detail jika add
                    if (save_method == 'add' && data.id_transfer) {
                        setTimeout(function() {
                            window.location.href = "<?php echo site_url('TransferStok/detail') ?>/" + data.id_transfer;
                        }, 2000);
                    }
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
                    }
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error adding / update data');
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            }
        });
    }

    function delete_data(id) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: 'Apakah Anda yakin ingin menghapus data transfer stok ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                // ajax delete data to database
                $.ajax({
                    url: "<?php echo site_url('TransferStok/delete') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function(data) {
                        if (data.status) {
                            Swal.fire({
                                title: 'Terhapus!',
                                text: 'Data transfer stok berhasil dihapus',
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });
                            reload_table();
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Gagal menghapus data',
                                icon: 'error'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Error deleting data',
                            icon: 'error'
                        });
                    }
                });
            }
        });
    }

    // Workflow functions
    function kirim(id) {
        Swal.fire({
            title: 'Konfirmasi Kirim',
            text: 'Apakah Anda yakin ingin mengirim transfer stok ini? Stok akan dikurangi dari gudang asal.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Kirim!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('TransferStok/kirim') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: { id: id },
                    success: function(data) {
                        if (data.status) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });
                            reload_table();
                        } else {
                            let errorMsg = data.message;
                            if (data.insufficient_items && data.insufficient_items.length > 0) {
                                errorMsg += '\n\nDetail kekurangan stok:';
                                data.insufficient_items.forEach(function(item) {
                                    errorMsg += '\n- ' + item.nama_barang + ': Diminta ' + item.qty_diminta + ', Tersedia ' + item.stok_tersedia;
                                });
                            }
                            Swal.fire({
                                title: 'Gagal!',
                                text: errorMsg,
                                icon: 'error'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat mengirim transfer',
                            icon: 'error'
                        });
                    }
                });
            }
        });
    }

    function terima(id) {
        Swal.fire({
            title: 'Konfirmasi Terima',
            text: 'Apakah Anda yakin ingin menerima transfer stok ini? Stok akan ditambahkan ke gudang tujuan.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Terima!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('TransferStok/terima') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: { id: id },
                    success: function(data) {
                        if (data.status) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });
                            reload_table();
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: data.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menerima transfer',
                            icon: 'error'
                        });
                    }
                });
            }
        });
    }

    function batal(id) {
        Swal.fire({
            title: 'Konfirmasi Batal',
            text: 'Apakah Anda yakin ingin membatalkan transfer stok ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Batalkan!',
            cancelButtonText: 'Tidak'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('TransferStok/batal') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: { id: id },
                    success: function(data) {
                        if (data.status) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });
                            reload_table();
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: data.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat membatalkan transfer',
                            icon: 'error'
                        });
                    }
                });
            }
        });
    }

    function detail(id) {
        // Load detail dalam modal
        $.ajax({
            url: "<?php echo site_url('TransferStok/detail') ?>/" + id,
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-detail .modal-content').html(data);
                $('#modal-detail').modal('show');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error loading detail: ' + textStatus);
            }
        });
    }

    function generateNomor() {
        $.ajax({
            url: "<?php echo site_url('TransferStok/generate_nomor') ?>",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="nomor_transfer"]').val(data.nomor);
            },
            error: function() {
                Swal.fire({
                    title: 'Error!',
                    text: 'Gagal generate nomor transfer',
                    icon: 'error'
                });
            }
        });
    }
</script>

<!-- Modal Detail Transfer Stok -->
<div class="modal fade" id="modal-detail" tabindex="-1" role="dialog" aria-labelledby="modal-detail-label" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <!-- Content akan dimuat via AJAX -->
        </div>
    </div>
</div>
