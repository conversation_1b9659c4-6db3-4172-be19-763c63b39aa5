<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TransferStok extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_transfer_stok', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        
        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'transfer_stok/transfer_stok', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_transfer_stok->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $transfer) {
            $no++;
            $row = array();
            $row[] = $transfer->nomor_transfer;
            $row[] = date('d/m/Y', strtotime($transfer->tanggal_transfer));
            $row[] = $transfer->nama_gudang_asal . '<br><small class="text-muted">' . $transfer->kode_gudang_asal . '</small>';
            $row[] = $transfer->nama_gudang_tujuan . '<br><small class="text-muted">' . $transfer->kode_gudang_tujuan . '</small>';
            
            // Status badge
            $status_class = '';
            switch ($transfer->status) {
                case 'draft':
                    $status_class = 'badge-secondary';
                    break;
                case 'dikirim':
                    $status_class = 'badge-warning';
                    break;
                case 'diterima':
                    $status_class = 'badge-success';
                    break;
                case 'batal':
                    $status_class = 'badge-danger';
                    break;
            }
            $row[] = '<span class="badge ' . $status_class . '">' . strtoupper($transfer->status) . '</span>';
            
            $row[] = number_format($transfer->total_item, 0);
            $row[] = number_format($transfer->total_qty, 2);
            
            // Action buttons
            $action = '';
            if ($transfer->status == 'draft') {
                $action .= '<button type="button" class="btn btn-sm btn-outline-warning" onclick="edit(' . $transfer->id . ')" title="Edit"><i class="fas fa-edit"></i></button> ';
                $action .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="delete_data(' . $transfer->id . ')" title="Delete"><i class="fas fa-trash"></i></button> ';
                $action .= '<button type="button" class="btn btn-sm btn-outline-primary" onclick="kirim(' . $transfer->id . ')" title="Kirim Transfer"><i class="fas fa-paper-plane"></i></button> ';
            } elseif ($transfer->status == 'dikirim') {
                $action .= '<button type="button" class="btn btn-sm btn-outline-success" onclick="terima(' . $transfer->id . ')" title="Terima Transfer"><i class="fas fa-check"></i></button> ';
                $action .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="batal(' . $transfer->id . ')" title="Batalkan"><i class="fas fa-times"></i></button> ';
            }
            $action .= '<button type="button" class="btn btn-sm btn-outline-info" onclick="detail(' . $transfer->id . ')" title="Detail"><i class="fas fa-eye"></i></button>';
            
            $row[] = $action;
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_transfer_stok->count_all(),
            "recordsFiltered" => $this->Mod_transfer_stok->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert()
    {
        $this->_validate();

        // Generate nomor otomatis jika tidak diisi
        $nomor = $this->input->post('nomor_transfer');
        if (empty($nomor)) {
            $nomor = $this->Mod_transfer_stok->generate_nomor();
        }

        $save = array(
            'nomor_transfer' => $nomor,
            'tanggal_transfer' => $this->input->post('tanggal_transfer'),
            'gudang_asal_id' => $this->input->post('gudang_asal_id'),
            'gudang_tujuan_id' => $this->input->post('gudang_tujuan_id'),
            'keterangan' => $this->input->post('keterangan'),
            'dibuat_oleh' => $this->session->userdata('nama_user'),
        );
        
        $id_transfer = $this->Mod_transfer_stok->insert($save);
        
        echo json_encode(array(
            "status" => TRUE,
            "id_transfer" => $id_transfer
        ));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');

        $save = array(
            'tanggal_transfer' => $this->input->post('tanggal_transfer'),
            'gudang_asal_id' => $this->input->post('gudang_asal_id'),
            'gudang_tujuan_id' => $this->input->post('gudang_tujuan_id'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_transfer_stok->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_transfer_stok->get($id);
        echo json_encode($data);
    }

    public function form_input()
    {
        // Load dropdown data untuk form
        $data['gudang_list'] = $this->Mod_transfer_stok->get_gudang_dropdown();
        $this->load->view('transfer_stok/form_input', $data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_transfer_stok->delete($id);
        echo json_encode(array("status" => TRUE));
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_transfer_stok->generate_nomor();
        echo json_encode(array('nomor' => $nomor));
    }

    // ========== WORKFLOW METHODS ==========

    // Kirim transfer (draft -> dikirim)
    public function kirim()
    {
        $id = $this->input->post('id');
        $user_kirim = $this->session->userdata('nama_user');
        
        $result = $this->Mod_transfer_stok->kirim_transfer($id, $user_kirim);
        
        if ($result['valid']) {
            echo json_encode(array("status" => TRUE, "message" => $result['message']));
        } else {
            echo json_encode(array("status" => FALSE, "message" => $result['message'], "insufficient_items" => $result['insufficient_items'] ?? null));
        }
    }

    // Terima transfer (dikirim -> diterima)
    public function terima()
    {
        $id = $this->input->post('id');
        $user_terima = $this->session->userdata('nama_user');
        
        $result = $this->Mod_transfer_stok->terima_transfer($id, $user_terima);
        
        if ($result['valid']) {
            echo json_encode(array("status" => TRUE, "message" => $result['message']));
        } else {
            echo json_encode(array("status" => FALSE, "message" => $result['message']));
        }
    }

    // Batalkan transfer
    public function batal()
    {
        $id = $this->input->post('id');
        $user_batal = $this->session->userdata('nama_user');
        
        $result = $this->Mod_transfer_stok->batal_transfer($id, $user_batal);
        
        if ($result['valid']) {
            echo json_encode(array("status" => TRUE, "message" => $result['message']));
        } else {
            echo json_encode(array("status" => FALSE, "message" => $result['message']));
        }
    }

    // Detail transfer stok (untuk modal)
    public function detail($id)
    {
        $data['transfer_data'] = $this->Mod_transfer_stok->get($id);
        $data['detail_list'] = $this->Mod_transfer_stok->get_detail($id);
        $this->load->view('transfer_stok/detail_modal', $data);
    }

    // Detail transfer stok (halaman penuh - untuk backward compatibility)
    public function detail_page($id)
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
        $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
        $akses = $a_submenu->view ?? 'N';

        /* $link = $this->uri->segment(1);
        $akses = $this->Mod_userlevel->cek_akses($link); */

        $data = array(
            'level' => $this->session->userdata('level'),
            'akses' => $akses,
            'link' => $link,
            'id_transfer' => $id,
            'transfer_data' => $this->Mod_transfer_stok->get($id),
            'barang_list' => $this->Mod_transfer_stok->get_barang_dropdown(),
            'gudang_list' => $this->Mod_transfer_stok->get_gudang_dropdown(),
            'satuan_list' => $this->Mod_transfer_stok->get_satuan_dropdown()
        );

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'transfer_stok/detail_transfer_stok', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    // ========== DETAIL METHODS ==========

    public function ajax_list_detail()
    {
        $transfer_stok_id = $this->input->post('transfer_stok_id');
        $list = $this->Mod_transfer_stok->get_detail($transfer_stok_id);
        $data = array();
        $no = 0;
        
        foreach ($list as $detail) {
            $no++;
            $row = array();
            $row[] = $no;
            $row[] = $detail->kode_barang . '<br><small class="text-muted">' . $detail->nama_barang . '</small>';
            $row[] = number_format($detail->qty, 2);
            $row[] = $detail->nama_satuan;
            $row[] = $detail->keterangan ?: '-';
            
            // Action buttons (hanya jika status masih draft)
            $transfer = $this->Mod_transfer_stok->get($transfer_stok_id);
            $action = '';
            if ($transfer && $transfer->status == 'draft') {
                $action .= '<button type="button" class="btn btn-sm btn-outline-warning" onclick="edit_detail(' . $detail->id . ')" title="Edit"><i class="fas fa-edit"></i></button> ';
                $action .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="delete_detail(' . $detail->id . ')" title="Delete"><i class="fas fa-trash"></i></button>';
            } else {
                $action = '<span class="text-muted">-</span>';
            }
            
            $row[] = $action;
            $data[] = $row;
        }

        $output = array(
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert_detail()
    {
        $this->_validate_detail();

        $save = array(
            'transfer_stok_id' => $this->input->post('transfer_stok_id'),
            'barang_id' => $this->input->post('barang_id'),
            'satuan_id' => $this->input->post('satuan_id'),
            'qty' => $this->input->post('qty'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_transfer_stok->insert_detail($save);
        echo json_encode(array("status" => TRUE));
    }

    public function update_detail()
    {
        $this->_validate_detail();

        $id = $this->input->post('id');

        $save = array(
            'barang_id' => $this->input->post('barang_id'),
            'satuan_id' => $this->input->post('satuan_id'),
            'qty' => $this->input->post('qty'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_transfer_stok->update_detail($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_transfer_stok->get_detail_by_id($id);
        echo json_encode($data);
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $this->Mod_transfer_stok->delete_detail($id);
        echo json_encode(array("status" => TRUE));
    }

    // Get barang detail untuk form
    public function get_barang_detail()
    {
        $id_barang = $this->input->post('id_barang');
        $data = $this->Mod_transfer_stok->get_barang_detail($id_barang);
        echo json_encode($data);
    }

    // Get stok barang di gudang tertentu
    public function get_stok_barang()
    {
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $stok = $this->Mod_transfer_stok->get_stok_barang($id_barang, $id_gudang);
        echo json_encode(array('stok' => $stok));
    }

    // Get barang dropdown untuk modal
    public function get_barang_dropdown()
    {
        $data = $this->Mod_transfer_stok->get_barang_dropdown();
        echo json_encode($data);
    }

    // Get barang dengan stok di gudang tertentu
    public function get_barang_with_stock()
    {
        $gudang_id = $this->input->post('gudang_id');
        $data = $this->Mod_transfer_stok->get_barang_with_stock($gudang_id);
        echo json_encode($data);
    }

    // Get barang dengan stok yang tersedia (exclude yang sudah dipilih)
    public function get_barang_with_stock_available()
    {
        $gudang_id = $this->input->post('gudang_id');
        $transfer_stok_id = $this->input->post('transfer_stok_id');
        $data = $this->Mod_transfer_stok->get_barang_with_stock_available($gudang_id, $transfer_stok_id);
        echo json_encode($data);
    }

    // Get satuan dropdown untuk modal
    public function get_satuan_dropdown()
    {
        $data = $this->Mod_transfer_stok->get_satuan_dropdown();
        echo json_encode($data);
    }

    // Cetak transfer stok
    public function cetak_transfer($id)
    {
        $data['transfer_data'] = $this->Mod_transfer_stok->get($id);
        $data['detail_list'] = $this->Mod_transfer_stok->get_detail($id);

        if (!$data['transfer_data']) {
            show_404();
            return;
        }

        $this->load->view('transfer_stok/cetak_transfer', $data);
    }

    // ========== VALIDATION METHODS ==========

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        if ($this->input->post('nomor_transfer') == '') {
            $data['inputerror'][] = 'nomor_transfer';
            $data['error_string'][] = 'Nomor transfer harus diisi';
            $data['status'] = FALSE;
        } else {
            // Check nomor exists
            $nomor = $this->input->post('nomor_transfer');
            $id = $this->input->post('id');
            if ($this->Mod_transfer_stok->check_nomor_exists($nomor, $id)) {
                $data['inputerror'][] = 'nomor_transfer';
                $data['error_string'][] = 'Nomor transfer sudah ada';
                $data['status'] = FALSE;
            }
        }

        if ($this->input->post('tanggal_transfer') == '') {
            $data['inputerror'][] = 'tanggal_transfer';
            $data['error_string'][] = 'Tanggal transfer harus diisi';
            $data['status'] = FALSE;
        }

        if ($this->input->post('gudang_asal_id') == '') {
            $data['inputerror'][] = 'gudang_asal_id';
            $data['error_string'][] = 'Gudang asal harus dipilih';
            $data['status'] = FALSE;
        }

        if ($this->input->post('gudang_tujuan_id') == '') {
            $data['inputerror'][] = 'gudang_tujuan_id';
            $data['error_string'][] = 'Gudang tujuan harus dipilih';
            $data['status'] = FALSE;
        }

        // Check gudang asal != gudang tujuan
        if ($this->input->post('gudang_asal_id') == $this->input->post('gudang_tujuan_id') &&
            $this->input->post('gudang_asal_id') != '') {
            $data['inputerror'][] = 'gudang_tujuan_id';
            $data['error_string'][] = 'Gudang tujuan harus berbeda dengan gudang asal';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    private function _validate_detail()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        $barang_id = $this->input->post('barang_id');
        $transfer_stok_id = $this->input->post('transfer_stok_id');
        $qty = $this->input->post('qty');
        $detail_id = $this->input->post('id'); // untuk update

        if ($barang_id == '') {
            $data['inputerror'][] = 'barang_id';
            $data['error_string'][] = 'Barang harus dipilih';
            $data['status'] = FALSE;
        } else {
            // Cek apakah barang sudah ada di detail transfer (untuk insert atau update dengan barang berbeda)
            $existing = $this->Mod_transfer_stok->check_barang_exists_in_detail($transfer_stok_id, $barang_id, $detail_id);
            if ($existing) {
                $data['inputerror'][] = 'barang_id';
                $data['error_string'][] = 'Barang sudah ada dalam daftar transfer';
                $data['status'] = FALSE;
            }
        }

        if ($this->input->post('satuan_id') == '') {
            $data['inputerror'][] = 'satuan_id';
            $data['error_string'][] = 'Satuan harus dipilih';
            $data['status'] = FALSE;
        }

        if ($qty == '' || $qty <= 0) {
            $data['inputerror'][] = 'qty';
            $data['error_string'][] = 'Quantity harus diisi dan lebih dari 0';
            $data['status'] = FALSE;
        } else if ($barang_id != '') {
            // Validasi quantity tidak melebihi stok tersedia
            $transfer_data = $this->Mod_transfer_stok->get($transfer_stok_id);
            if ($transfer_data) {
                $stok_tersedia = $this->Mod_transfer_stok->get_stok_barang($barang_id, $transfer_data->gudang_asal_id);
                if ($qty > $stok_tersedia) {
                    $data['inputerror'][] = 'qty';
                    $data['error_string'][] = 'Quantity tidak boleh melebihi stok tersedia (' . $stok_tersedia . ')';
                    $data['status'] = FALSE;
                }
            }
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }
}
