<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Cetak Transfer Stok - <?= $transfer_data->nomor_transfer ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .document-title {
            font-size: 16px;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .info-section {
            margin-bottom: 20px;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .info-table td {
            padding: 5px;
            vertical-align: top;
        }
        
        .info-table .label {
            width: 120px;
            font-weight: bold;
        }
        
        .info-table .colon {
            width: 10px;
        }
        
        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .detail-table th,
        .detail-table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        
        .detail-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }
        
        .detail-table .text-center {
            text-align: center;
        }
        
        .detail-table .text-right {
            text-align: right;
        }
        
        .status-badge {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .status-draft {
            background-color: #6c757d;
            color: white;
        }
        
        .status-dikirim {
            background-color: #ffc107;
            color: #212529;
        }
        
        .status-diterima {
            background-color: #28a745;
            color: white;
        }
        
        .status-dibatalkan {
            background-color: #dc3545;
            color: white;
        }
        
        .footer {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            width: 200px;
            text-align: center;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 60px;
            padding-top: 5px;
        }
        
        @media print {
            body {
                margin: 0;
            }
            
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">TOKO ELEKTRONIK</div>
        <div class="document-title">SURAT TRANSFER STOK</div>
    </div>

    <div class="info-section">
        <table class="info-table">
            <tr>
                <td class="label">Nomor Transfer</td>
                <td class="colon">:</td>
                <td><?= $transfer_data->nomor_transfer ?></td>
                <td class="label">Status</td>
                <td class="colon">:</td>
                <td>
                    <?php
                    $status_class = '';
                    $status_text = '';
                    switch ($transfer_data->status) {
                        case 'draft':
                            $status_class = 'status-draft';
                            $status_text = 'Draft';
                            break;
                        case 'dikirim':
                            $status_class = 'status-dikirim';
                            $status_text = 'Dikirim';
                            break;
                        case 'diterima':
                            $status_class = 'status-diterima';
                            $status_text = 'Diterima';
                            break;
                        case 'dibatalkan':
                            $status_class = 'status-dibatalkan';
                            $status_text = 'Dibatalkan';
                            break;
                        default:
                            $status_class = 'status-draft';
                            $status_text = ucfirst($transfer_data->status);
                    }
                    ?>
                    <span class="status-badge <?= $status_class ?>"><?= $status_text ?></span>
                </td>
            </tr>
            <tr>
                <td class="label">Tanggal Transfer</td>
                <td class="colon">:</td>
                <td><?= date('d/m/Y', strtotime($transfer_data->tanggal_transfer)) ?></td>
                <td class="label">Dibuat Oleh</td>
                <td class="colon">:</td>
                <td><?= $transfer_data->dibuat_oleh ?: '-' ?></td>
            </tr>
            <tr>
                <td class="label">Gudang Asal</td>
                <td class="colon">:</td>
                <td><?= $transfer_data->nama_gudang_asal ?></td>
                <td class="label">Gudang Tujuan</td>
                <td class="colon">:</td>
                <td><?= $transfer_data->nama_gudang_tujuan ?></td>
            </tr>
            <tr>
                <td class="label">Keterangan</td>
                <td class="colon">:</td>
                <td colspan="4"><?= $transfer_data->keterangan ?: '-' ?></td>
            </tr>
        </table>
    </div>

    <table class="detail-table">
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="15%">Kode Barang</th>
                <th width="40%">Nama Barang</th>
                <th width="12%">Quantity</th>
                <th width="10%">Satuan</th>
                <th width="18%">Keterangan</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($detail_list)): ?>
                <?php $no = 1; $total_items = 0; ?>
                <?php foreach ($detail_list as $detail): ?>
                <tr>
                    <td class="text-center"><?= $no++ ?></td>
                    <td><?= $detail->kode_barang ?></td>
                    <td><?= $detail->nama_barang ?></td>
                    <td class="text-right"><?= number_format($detail->qty, 2) ?></td>
                    <td class="text-center"><?= $detail->nama_satuan ?></td>
                    <td><?= $detail->keterangan ?: '-' ?></td>
                </tr>
                <?php $total_items += $detail->qty; ?>
                <?php endforeach; ?>
                <tr style="background-color: #f8f9fa; font-weight: bold;">
                    <td colspan="3" class="text-center">TOTAL</td>
                    <td class="text-right"><?= number_format($total_items, 2) ?></td>
                    <td colspan="2"></td>
                </tr>
            <?php else: ?>
                <tr>
                    <td colspan="6" class="text-center">Tidak ada detail barang</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <div class="footer">
        <div class="signature-box">
            <div>Pengirim</div>
            <div class="signature-line">
                <?= $transfer_data->dikirim_oleh ?: '(...............................)' ?>
            </div>
        </div>
        
        <div class="signature-box">
            <div>Penerima</div>
            <div class="signature-line">
                <?= $transfer_data->diterima_oleh ?: '(...............................)' ?>
            </div>
        </div>
    </div>

    <div class="no-print" style="margin-top: 30px; text-align: center;">
        <button onclick="window.print()" style="padding: 10px 20px; font-size: 14px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Cetak Dokumen
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; font-size: 14px; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Tutup
        </button>
    </div>

    <script>
        // Auto print saat halaman dimuat
        window.onload = function() {
            // Uncomment baris berikut jika ingin auto print
            // window.print();
        }
    </script>
</body>
</html>
