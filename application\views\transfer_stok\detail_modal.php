<!-- Modal Detail Transfer Stok -->
<div class="modal-header">
    <h4 class="modal-title">Detail Transfer Stok</h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="modal-body">
    <?php if ($transfer_data): ?>
    <!-- Header Information -->
    <div class="row mb-3">
        <div class="col-md-6">
            <table class="table table-borderless table-sm">
                <tr>
                    <td width="40%"><strong>Nomor Transfer</strong></td>
                    <td width="5%">:</td>
                    <td><?= $transfer_data->nomor_transfer ?></td>
                </tr>
                <tr>
                    <td><strong>Tanggal Transfer</strong></td>
                    <td>:</td>
                    <td><?= date('d/m/Y', strtotime($transfer_data->tanggal_transfer)) ?></td>
                </tr>
                <tr>
                    <td><strong>Status</strong></td>
                    <td>:</td>
                    <td>
                        <?php
                        $status_class = '';
                        $status_text = '';
                        switch ($transfer_data->status) {
                            case 'draft':
                                $status_class = 'badge-secondary';
                                $status_text = 'Draft';
                                break;
                            case 'dikirim':
                                $status_class = 'badge-warning';
                                $status_text = 'Dikirim';
                                break;
                            case 'diterima':
                                $status_class = 'badge-success';
                                $status_text = 'Diterima';
                                break;
                            case 'dibatalkan':
                                $status_class = 'badge-danger';
                                $status_text = 'Dibatalkan';
                                break;
                            default:
                                $status_class = 'badge-light';
                                $status_text = ucfirst($transfer_data->status);
                        }
                        ?>
                        <span class="badge <?= $status_class ?>"><?= $status_text ?></span>
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <table class="table table-borderless table-sm">
                <tr>
                    <td width="40%"><strong>Gudang Asal</strong></td>
                    <td width="5%">:</td>
                    <td><?= $transfer_data->nama_gudang_asal ?></td>
                </tr>
                <tr>
                    <td><strong>Gudang Tujuan</strong></td>
                    <td>:</td>
                    <td><?= $transfer_data->nama_gudang_tujuan ?></td>
                </tr>
                <tr>
                    <td><strong>Keterangan</strong></td>
                    <td>:</td>
                    <td><?= $transfer_data->keterangan ?: '-' ?></td>
                </tr>
            </table>
        </div>
    </div>

    <hr>

    <!-- Detail Items -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h5 class="mb-0">Detail Barang</h5>
        <?php if ($transfer_data && $transfer_data->status == 'draft'): ?>
        <button type="button" class="btn btn-sm btn-primary" onclick="add_detail_modal()">
            <i class="fas fa-plus"></i> Tambah Barang
        </button>
        <?php endif; ?>
    </div>

    <div class="table-responsive">
        <table class="table table-bordered table-striped table-sm" id="table-detail-modal">
            <thead class="thead-light">
                <tr>
                    <th width="5%">No</th>
                    <th width="20%">Kode Barang</th>
                    <th width="30%">Nama Barang</th>
                    <th width="12%">Qty</th>
                    <th width="10%">Satuan</th>
                    <th width="15%">Keterangan</th>
                    <?php if ($transfer_data && $transfer_data->status == 'draft'): ?>
                    <th width="8%">Aksi</th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($detail_list)): ?>
                    <?php $no = 1; foreach ($detail_list as $detail): ?>
                    <tr>
                        <td><?= $no++ ?></td>
                        <td><?= $detail->kode_barang ?></td>
                        <td><?= $detail->nama_barang ?></td>
                        <td class="text-right"><?= number_format($detail->qty, 2) ?></td>
                        <td><?= $detail->nama_satuan ?></td>
                        <td><?= $detail->keterangan ?: '-' ?></td>
                        <?php if ($transfer_data && $transfer_data->status == 'draft'): ?>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="edit_detail_modal(<?= $detail->id ?>)" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="delete_detail_modal(<?= $detail->id ?>)" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                        <?php endif; ?>
                    </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="<?= ($transfer_data && $transfer_data->status == 'draft') ? '7' : '6' ?>" class="text-center text-muted">Tidak ada detail barang</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Timeline Status (jika ada) -->
    <?php if ($transfer_data->status != 'draft'): ?>
    <hr>
    <h5>Timeline Status</h5>
    <div class="timeline">
        <div class="timeline-item">
            <div class="timeline-marker bg-secondary"></div>
            <div class="timeline-content">
                <h6 class="timeline-title">Draft</h6>
                <p class="timeline-text">Transfer stok dibuat</p>
                <small class="text-muted"><?= isset($transfer_data->created_at) ? date('d/m/Y H:i', strtotime($transfer_data->created_at)) : '-' ?></small>
            </div>
        </div>
        
        <?php if ($transfer_data->status == 'dikirim' || $transfer_data->status == 'diterima'): ?>
        <div class="timeline-item">
            <div class="timeline-marker bg-warning"></div>
            <div class="timeline-content">
                <h6 class="timeline-title">Dikirim</h6>
                <p class="timeline-text">Barang telah dikirim dari gudang asal</p>
                <small class="text-muted"><?= $transfer_data->tanggal_kirim ? date('d/m/Y H:i', strtotime($transfer_data->tanggal_kirim)) : '-' ?></small>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if ($transfer_data->status == 'diterima'): ?>
        <div class="timeline-item">
            <div class="timeline-marker bg-success"></div>
            <div class="timeline-content">
                <h6 class="timeline-title">Diterima</h6>
                <p class="timeline-text">Barang telah diterima di gudang tujuan</p>
                <small class="text-muted"><?= $transfer_data->tanggal_terima ? date('d/m/Y H:i', strtotime($transfer_data->tanggal_terima)) : '-' ?></small>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if ($transfer_data->status == 'dibatalkan'): ?>
        <div class="timeline-item">
            <div class="timeline-marker bg-danger"></div>
            <div class="timeline-content">
                <h6 class="timeline-title">Dibatalkan</h6>
                <p class="timeline-text">Transfer stok dibatalkan</p>
                <small class="text-muted"><?= $transfer_data->tanggal_batal ? date('d/m/Y H:i', strtotime($transfer_data->tanggal_batal)) : '-' ?></small>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php else: ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i> Data transfer stok tidak ditemukan.
    </div>
    <?php endif; ?>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
    <?php if ($transfer_data && $transfer_data->status != 'dibatalkan'): ?>
    <button type="button" class="btn btn-primary" onclick="window.open('<?= site_url('TransferStok/cetak_transfer/' . $transfer_data->id) ?>', '_blank')">
        <i class="fas fa-print"></i> Cetak
    </button>
    <?php endif; ?>
</div>

<!-- Modal Form Detail Barang -->
<div class="modal fade" id="modal-form-detail" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-form-detail-title">Tambah Detail Barang</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-detail-modal">
                <div class="modal-body">
                    <input type="hidden" id="detail_id" name="id">
                    <input type="hidden" id="transfer_stok_id_detail" name="transfer_stok_id" value="<?= $transfer_data ? $transfer_data->id : '' ?>">

                    <div class="form-group">
                        <label for="barang_id_detail">Barang <span class="text-danger">*</span></label>
                        <select class="form-control select2" id="barang_id_detail" name="barang_id" style="width: 100%;">
                            <option value="">Pilih Barang</option>
                        </select>
                        <span class="help-block"></span>
                    </div>

                    <div class="form-group">
                        <label for="satuan_id_detail">Satuan <span class="text-danger">*</span></label>
                        <input type="hidden" id="satuan_id_detail" name="satuan_id">
                        <input type="text" class="form-control" id="satuan_nama_detail" readonly placeholder="Satuan akan otomatis terisi">
                        <span class="help-block"></span>
                    </div>

                    <div class="form-group">
                        <label for="qty_detail">Quantity <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="qty_detail" name="qty" step="0.01" min="0.01" max="">
                        <span class="help-block"></span>
                    </div>

                    <div class="form-group">
                        <label for="keterangan_detail">Keterangan</label>
                        <textarea class="form-control" id="keterangan_detail" name="keterangan" rows="3"></textarea>
                        <span class="help-block"></span>
                    </div>

                    <div class="form-group" id="stok-info" style="display: none;">
                        <div class="alert alert-info">
                            <strong>Stok Tersedia:</strong> <span id="stok-tersedia">0</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="cancel_detail_modal()">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 3px solid #dee2e6;
}

.timeline-title {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin: 0 0 5px 0;
    font-size: 13px;
    color: #6c757d;
}
</style>

<script>
var save_method_detail_modal;
var current_transfer_id = <?= $transfer_data ? $transfer_data->id : 0 ?>;

$(document).ready(function() {
    // Load dropdown data saat modal detail dibuka
    load_barang_with_stock();

    // Handle form submit
    $('#form-detail-modal').on('submit', function(e) {
        e.preventDefault();
        save_detail_modal();
    });

    // Setup event handlers
    setupEventHandlers();
});

function setupEventHandlers() {
    // Remove existing handlers to prevent duplicate
    $('#barang_id_detail').off('change.detailModal');
    $('#qty_detail').off('input.detailModal');

    // Handle barang change untuk load satuan dan stok
    $('#barang_id_detail').on('change.detailModal', function() {
        var barang_id = $(this).val();
        console.log('Barang changed:', barang_id); // Debug log

        if (barang_id) {
            // Get selected option data
            var selectedOption = $(this).find('option:selected');
            var satuan_id = selectedOption.data('satuan-id');
            var satuan_nama = selectedOption.data('satuan-nama');
            var stok_tersedia = selectedOption.data('stok');

            console.log('Satuan data:', {satuan_id: satuan_id, satuan_nama: satuan_nama, stok: stok_tersedia}); // Debug log

            // Set satuan otomatis
            $('#satuan_id_detail').val(satuan_id || '');
            $('#satuan_nama_detail').val(satuan_nama || '');

            // Tampilkan stok dan set max quantity
            $('#stok-tersedia').text(stok_tersedia || 0);
            $('#qty_detail').attr('max', stok_tersedia || 0);
            $('#stok-info').show();
        } else {
            $('#satuan_id_detail').val('');
            $('#satuan_nama_detail').val('');
            $('#qty_detail').removeAttr('max');
            $('#stok-info').hide();
        }
    });

    // Validasi quantity tidak melebihi stok
    $('#qty_detail').on('input.detailModal', function() {
        var qty = parseFloat($(this).val()) || 0;
        var max_qty = parseFloat($(this).attr('max')) || 0;

        if (qty > max_qty && max_qty > 0) {
            $(this).val(max_qty);
            Swal.fire('Peringatan!', 'Quantity tidak boleh melebihi stok tersedia (' + max_qty + ')', 'warning');
        }
    });
}

function add_detail_modal() {
    save_method_detail_modal = 'add';
    $('#form-detail-modal')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();
    $('#modal-form-detail-title').text('Tambah Detail Barang');
    $('#stok-info').hide();
    $('#qty_detail').removeAttr('max');

    // Reset satuan fields
    $('#satuan_id_detail').val('');
    $('#satuan_nama_detail').val('');

    // Reload barang list untuk exclude yang sudah dipilih
    load_barang_with_stock();

    // Setup event handlers setelah load barang
    setTimeout(function() {
        setupEventHandlers();
    }, 100);

    $('#modal-form-detail').modal('show');
}

function edit_detail_modal(id) {
    save_method_detail_modal = 'update';
    $('#form-detail-modal')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();
    $('#modal-form-detail-title').text('Edit Detail Barang');

    // Load data detail
    $.ajax({
        url: "<?= site_url('TransferStok/edit_detail') ?>/" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('#detail_id').val(data.id);

            // Set barang dan trigger change untuk load stok
            $('#barang_id_detail').val(data.barang_id);

            // Set satuan dan qty
            setTimeout(function() {
                $('#satuan_id_detail').val(data.satuan_id);
                $('#qty_detail').val(data.qty);
                $('#keterangan_detail').val(data.keterangan);

                // Setup event handlers untuk edit
                setupEventHandlers();

                // Trigger change untuk update stok info
                $('#barang_id_detail').trigger('change.detailModal');
            }, 100);

            $('#modal-form-detail').modal('show');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            alert('Error loading data: ' + textStatus);
        }
    });
}

function delete_detail_modal(id) {
    Swal.fire({
        title: 'Konfirmasi Hapus',
        text: 'Apakah Anda yakin ingin menghapus detail barang ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?= site_url('TransferStok/delete_detail') ?>",
                type: "POST",
                dataType: "JSON",
                data: { id: id },
                success: function(data) {
                    if (data.status) {
                        Swal.fire('Berhasil!', 'Detail barang berhasil dihapus.', 'success');
                        reload_detail_table();
                    } else {
                        Swal.fire('Error!', 'Gagal menghapus detail barang.', 'error');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire('Error!', 'Terjadi kesalahan: ' + textStatus, 'error');
                }
            });
        }
    });
}

function save_detail_modal() {
    var url;
    if (save_method_detail_modal == 'add') {
        url = "<?= site_url('TransferStok/insert_detail') ?>";
    } else {
        url = "<?= site_url('TransferStok/update_detail') ?>";
    }

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form-detail-modal').serialize(),
        dataType: "JSON",
        success: function(data) {
            if (data.status) {
                $('#modal-form-detail').modal('hide');
                Swal.fire('Berhasil!', 'Detail barang berhasil disimpan.', 'success');
                reload_detail_table();

                // Reset form untuk persiapan input berikutnya
                $('#form-detail-modal')[0].reset();
                $('.form-group').removeClass('has-error');
                $('.help-block').empty();
                $('#stok-info').hide();
                $('#qty_detail').removeAttr('max');
                $('#satuan_id_detail').val('');
                $('#satuan_nama_detail').val('');
            } else {
                for (var i = 0; i < data.inputerror.length; i++) {
                    $('[name="' + data.inputerror[i] + '"]').parent().addClass('has-error');
                    $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                }
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire('Error!', 'Terjadi kesalahan: ' + textStatus, 'error');
        }
    });
}

function load_barang_with_stock() {
    <?php if ($transfer_data): ?>
    $.ajax({
        url: "<?= site_url('TransferStok/get_barang_with_stock_available') ?>",
        type: "POST",
        data: {
            gudang_id: <?= $transfer_data->gudang_asal_id ?>,
            transfer_stok_id: current_transfer_id
        },
        dataType: "JSON",
        success: function(data) {
            console.log('Loaded barang data:', data); // Debug log
            var options = '<option value="">Pilih Barang</option>';
            $.each(data, function(index, item) {
                var satuan_nama = item.nama_satuan || '';
                options += '<option value="' + item.id + '" ' +
                          'data-satuan-id="' + (item.satuan_id || '') + '" ' +
                          'data-satuan-nama="' + satuan_nama + '" ' +
                          'data-stok="' + (item.stok_tersedia || 0) + '">' +
                          item.kode_barang + ' - ' + item.nama_barang + ' (Stok: ' + item.stok_tersedia + ')</option>';
            });
            $('#barang_id_detail').html(options);

            // Setup event handlers setelah load data
            setupEventHandlers();
        },
        error: function() {
            $('#barang_id_detail').html('<option value="">Error loading barang</option>');
        }
    });
    <?php else: ?>
    $('#barang_id_detail').html('<option value="">Gudang asal tidak ditemukan</option>');
    <?php endif; ?>
}

// Function untuk cancel modal tanpa menutup parent modal
function cancel_detail_modal() {
    $('#modal-form-detail').modal('hide');
}

// Function load_satuan_dropdown, load_barang_satuan dan load_stok_barang sudah tidak diperlukan
// karena data sudah diambil langsung dari dropdown barang dengan stok dan satuan readonly

function reload_detail_table() {
    // Reload modal detail dengan data terbaru
    detail(current_transfer_id);
}
</script>
